import React, { useState, useEffect, useMemo, useCallback } from 'react'
import FolderTree from './components/FolderTree'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardTitle } from '@/components/ui/card'
import { <PERSON>rollArea } from '@/components/ui/scroll-area'

import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { SettingsPopup } from '@/components/settings-popup'
import { Folder, FolderOpen, Search, Tag, X, ChevronLeft, ChevronUp, Settings } from 'lucide-react'

// Define interfaces with strict TypeScript standards
interface Photo {
  id: number
  path: string
  date: string
  thumbnail_path?: string
}

interface FolderNode {
  name: string
  path: string
  isDirectory: boolean
  children?: FolderNode[]
}

interface ElectronAPI {
  ipcRenderer: {
    invoke(channel: 'select-folder'): Promise<string | null>
    invoke(
      channel: 'get-photos',
      args: { offset: number; limit: number; tags: string[]; folderPath?: string }
    ): Promise<Photo[]>
    invoke(channel: 'get-folder-tree', folderPath: string): Promise<FolderNode[] | null>
    invoke(channel: 'get-photo-tags', photoId: number): Promise<string[]>
    invoke(channel: 'get-all-tags'): Promise<string[]>
    invoke(channel: 'add-tag-to-photo', args: { photoId: number; tagName: string }): Promise<void>
    invoke(
      channel: 'remove-tag-from-photo',
      args: { photoId: number; tagName: string }
    ): Promise<void>
    invoke(channel: 'open-file-in-explorer', filePath: string): Promise<void>
    on(channel: string, listener: (...args: unknown[]) => void): () => void
  }
  process: {
    versions: {
      electron: string
      chrome: string
      node: string
    }
  }
}

declare global {
  interface Window {
    electron: ElectronAPI
  }
}

const App: React.FC = () => {
  const [photos, setPhotos] = useState<Photo[]>([])
  const [loadingState, setLoadingState] = useState<{
    photos: boolean
    folders: boolean
    tags: boolean
    scanning: boolean
    loadingMore: boolean
  }>({
    photos: false,
    folders: false,
    tags: false,
    scanning: false,
    loadingMore: false
  })
  const [error, setError] = useState<string | null>(null)
  const [selectedFolder, setSelectedFolder] = useState<string | null>(null)
  const [rootFolder, setRootFolder] = useState<string | null>(null) // The initially selected folder
  const [folderTree, setFolderTree] = useState<FolderNode[] | null>(null)
  const [selectedPhoto, setSelectedPhoto] = useState<Photo | null>(null)
  const [photoTags, setPhotoTags] = useState<string[]>([])
  const [allTags, setAllTags] = useState<string[]>([])
  const [searchTags, setSearchTags] = useState<string[]>([])
  const [newTag, setNewTag] = useState<string>('')
  const [isFullscreenViewer, setIsFullscreenViewer] = useState<boolean>(false)
  const [currentPhotoIndex, setCurrentPhotoIndex] = useState<number>(0)
  const [hasMorePhotos, setHasMorePhotos] = useState<boolean>(true)
  // const [totalPhotos, setTotalPhotos] = useState<number>(0)
  const [showTagSuggestions, setShowTagSuggestions] = useState<boolean>(false)
  const [filteredTags, setFilteredTags] = useState<string[]>([])
  const [showTagsPanel, setShowTagsPanel] = useState<boolean>(true)
  const [tagSearchInput, setTagSearchInput] = useState<string>('')

  const selectFolder = async (): Promise<void> => {
    try {
      setLoadingState((prev) => ({ ...prev, scanning: true }))
      setError(null)

      const folderPath = await window.electron.ipcRenderer.invoke('select-folder')
      if (folderPath) {
        setSelectedFolder(folderPath)
        setRootFolder(folderPath) // Set as the root folder for the tree

        // Load folder tree
        setLoadingState((prev) => ({ ...prev, folders: true }))
        const tree = await window.electron.ipcRenderer.invoke('get-folder-tree', folderPath)
        setFolderTree(tree)

        // Load photos
        setLoadingState((prev) => ({ ...prev, photos: true }))
        await loadPhotos(folderPath, searchTags)

        // Load all tags
        setLoadingState((prev) => ({ ...prev, tags: true }))
        const tags = await window.electron.ipcRenderer.invoke('get-all-tags')
        setAllTags(tags)
      }
    } catch (err) {
      console.error('Error selecting folder:', err)
      setError('Failed to scan folder. Please try again.')
    } finally {
      setLoadingState({
        photos: false,
        folders: false,
        tags: false,
        scanning: false,
        loadingMore: false
      })
    }
  }

  // Load photos with pagination for better performance
  const loadPhotos = useCallback(
    async (folderPath?: string, tags?: string[], reset: boolean = true): Promise<void> => {
      if (!folderPath && !selectedFolder) return

      try {
        setLoadingState((prev) => ({ ...prev, photos: reset, loadingMore: !reset }))
        setError(null)

        const pathToUse: string = folderPath || selectedFolder!
        const tagsToUse: string[] = tags || []
        const currentOffset = reset ? 0 : photos.length
        const batchSize = 100 // Load 100 photos at a time

        const fetchedPhotos: Photo[] = await window.electron.ipcRenderer.invoke('get-photos', {
          offset: currentOffset,
          limit: batchSize,
          tags: tagsToUse,
          folderPath: pathToUse
        })

        if (reset) {
          setPhotos(fetchedPhotos)
          // setTotalPhotos(fetchedPhotos.length)
        } else {
          setPhotos((prev) => [...prev, ...fetchedPhotos])
        }

        setHasMorePhotos(fetchedPhotos.length === batchSize)
      } catch (err) {
        console.error('Error loading photos:', err)
        setError('Failed to load photos. Please try again.')
      } finally {
        setLoadingState((prev) => ({ ...prev, photos: false, loadingMore: false }))
      }
    },
    [selectedFolder, photos.length]
  )

  const handleFolderSelect = async (path: string): Promise<void> => {
    setSelectedFolder(path)
    await loadPhotos(path, searchTags)
  }

  const openPhotoView = async (photo: Photo): Promise<void> => {
    const photoIndex = photos.findIndex((p) => p.id === photo.id)
    setCurrentPhotoIndex(photoIndex)
    setSelectedPhoto(photo)
    setIsFullscreenViewer(true)
    const tags: string[] = await window.electron.ipcRenderer.invoke('get-photo-tags', photo.id)
    setPhotoTags(tags)
    setNewTag('')
  }

  const navigatePhoto = useCallback((direction: 'prev' | 'next'): void => {
    const newIndex =
      direction === 'prev'
        ? Math.max(0, currentPhotoIndex - 1)
        : Math.min(photos.length - 1, currentPhotoIndex + 1)

    if (newIndex !== currentPhotoIndex) {
      setCurrentPhotoIndex(newIndex)
      const newPhoto = photos[newIndex]
      setSelectedPhoto(newPhoto)

      // Load tags for the new photo
      window.electron.ipcRenderer
        .invoke('get-photo-tags', newPhoto.id)
        .then((tags) => setPhotoTags(tags))
        .catch((error) => {
          console.error('Error loading photo tags:', error)
          setPhotoTags([])
        })
    }
  }, [currentPhotoIndex, photos])

  const closeFullscreenViewer = (): void => {
    setIsFullscreenViewer(false)
    setSelectedPhoto(null)
    setPhotoTags([])
  }

  // Add tag instantly without loading into text field
  const addTagInstantly = useCallback(
    async (tagName: string): Promise<void> => {
      if (tagName.trim() && selectedPhoto && !photoTags.includes(tagName.trim())) {
        await window.electron.ipcRenderer.invoke('add-tag-to-photo', {
          photoId: selectedPhoto.id,
          tagName: tagName.trim()
        })
        const updatedTags: string[] = await window.electron.ipcRenderer.invoke(
          'get-photo-tags',
          selectedPhoto.id
        )
        setPhotoTags(updatedTags)
        const allUpdatedTags: string[] = await window.electron.ipcRenderer.invoke('get-all-tags')
        setAllTags(allUpdatedTags)
      }
    },
    [selectedPhoto, photoTags]
  )

  // Add tag from text field
  const addTag = async (): Promise<void> => {
    if (newTag.trim() && selectedPhoto) {
      await addTagInstantly(newTag.trim())
      setNewTag('')
      setShowTagSuggestions(false)
    }
  }

  // Filter tags based on input
  const filterTags = useCallback(
    (input: string): void => {
      if (!input.trim()) {
        setFilteredTags([])
        setShowTagSuggestions(false)
        return
      }

      const filtered = allTags
        .filter(
          (tag) => !photoTags.includes(tag) && tag.toLowerCase().includes(input.toLowerCase())
        )
        .slice(0, 8) // Limit to 8 suggestions

      setFilteredTags(filtered)
      setShowTagSuggestions(filtered.length > 0)
    },
    [allTags, photoTags]
  )

  // Handle tag input change
  const handleTagInputChange = useCallback(
    (value: string): void => {
      setNewTag(value)
      filterTags(value)
    },
    [filterTags]
  )

  // Open file in Windows Explorer
  const openInExplorer = useCallback(async (): Promise<void> => {
    if (selectedPhoto) {
      try {
        await window.electron.ipcRenderer.invoke('open-file-in-explorer', selectedPhoto.path)
      } catch (error) {
        console.error('Failed to open file in explorer:', error)
      }
    }
  }, [selectedPhoto])

  // Get filtered tags for display based on search input
  const displayTags = useMemo(() => {
    if (!tagSearchInput.trim()) {
      return allTags
    }
    return allTags.filter((tag) => tag.toLowerCase().includes(tagSearchInput.toLowerCase()))
  }, [allTags, tagSearchInput])

  const removeTag = async (tagName: string): Promise<void> => {
    if (selectedPhoto) {
      await window.electron.ipcRenderer.invoke('remove-tag-from-photo', {
        photoId: selectedPhoto.id,
        tagName
      })
      const updatedTags: string[] = await window.electron.ipcRenderer.invoke(
        'get-photo-tags',
        selectedPhoto.id
      )
      setPhotoTags(updatedTags)
      const allUpdatedTags: string[] = await window.electron.ipcRenderer.invoke('get-all-tags')
      setAllTags(allUpdatedTags)
    }
  }

  // const clearAllThumbnails = async (): Promise<void> => {
  //   if (
  //     !confirm(
  //       'Are you sure you want to clear all thumbnail information? This will regenerate all thumbnails.'
  //     )
  //   ) {
  //     return
  //   }

  //   try {
  //     const result = await window.electron.ipcRenderer.invoke('clear-all-thumbnails')
  //     if (result.success) {
  //       console.log('Thumbnails cleared successfully')
  //       // Reload photos to show placeholder state
  //       await loadPhotos(selectedFolder, searchTags)
  //     } else {
  //       setError('Failed to clear thumbnails: ' + result.message)
  //     }
  //   } catch (error) {
  //     console.error('Error clearing thumbnails:', error)
  //     setError('Failed to clear thumbnails. Please try again.')
  //   }
  // }

  const handleSearch = (selectedOptions: string[]): void => {
    setSearchTags(selectedOptions)
    loadPhotos(selectedFolder || undefined, selectedOptions)
  }

  useEffect(() => {
    loadPhotos()
  }, [selectedFolder])

  // Listen for real-time thumbnail updates
  useEffect(() => {
    const handleThumbnailGenerated = (data: { photoId: number; thumbnailPath: string }): void => {
      console.log('Thumbnail generated for photo:', data.photoId, data.thumbnailPath)
      setPhotos((prevPhotos) =>
        prevPhotos.map((photo) =>
          photo.id === data.photoId ? { ...photo, thumbnail_path: data.thumbnailPath } : photo
        )
      )
    }

    // Set up IPC listeners
    const removeGeneratedListener = window.electron.ipcRenderer.on(
      'thumbnail-generated',
      (...args: unknown[]) => {
        const data = args[0] as { photoId: number; thumbnailPath: string }
        handleThumbnailGenerated(data)
      }
    )

    return () => {
      removeGeneratedListener()
    }
  }, [])

  // Keyboard navigation for fullscreen viewer
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent): void => {
      if (isFullscreenViewer) {
        switch (event.key) {
          case 'Escape':
            closeFullscreenViewer()
            break
          case 'ArrowLeft':
            navigatePhoto('prev')
            break
          case 'ArrowRight':
            navigatePhoto('next')
            break
        }
      }
    }

    window.addEventListener('keydown', handleKeyDown)
    return () => window.removeEventListener('keydown', handleKeyDown)
  }, [isFullscreenViewer, currentPhotoIndex, photos])

  return (
    <div className="flex h-screen bg-background text-foreground">
      {/* Material Design Sidebar */}
      <aside className="w-80 border-r bg-card shadow-sm">
        <div className="p-4 border-b bg-gradient-to-r from-primary/5 to-primary/10">
          <Button
            onClick={selectFolder}
            disabled={loadingState.scanning}
            className="w-full gap-2 shadow-sm"
            size="default"
          >
            {loadingState.scanning ? (
              <>
                <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                Scanning...
              </>
            ) : (
              <>
                <Folder className="w-4 h-4" />
                Select Folder
              </>
            )}
          </Button>
        </div>

        {/* Parent Folder Button */}
        {selectedFolder && rootFolder && selectedFolder !== rootFolder && (
          <div className="px-4 py-2 border-b">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => {
                const parentPath = selectedFolder.split('\\').slice(0, -1).join('\\')
                if (parentPath && parentPath.length >= rootFolder.length) {
                  handleFolderSelect(parentPath)
                }
              }}
              disabled={selectedFolder === rootFolder}
              className="w-full justify-start gap-2 h-8"
              title="Go to parent folder"
            >
              <div className="relative">
                <Folder className="w-4 h-4" />
                <ChevronUp className="w-2.5 h-2.5 absolute -top-0.5 -right-0.5" />
              </div>
              <span className="text-sm">Parent Folder</span>
            </Button>
          </div>
        )}

        <ScrollArea className="h-[calc(100vh-88px)]">
          <div className="p-4">
          {loadingState.scanning ? (
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-3 text-sm">
                  <div className="w-4 h-4 border-2 border-primary/30 border-t-primary rounded-full animate-spin"></div>
                  Scanning folder...
                </div>
              </CardContent>
            </Card>
          ) : error ? (
            <Card className="border-destructive">
              <CardContent className="p-4">
                <div className="flex items-start gap-2">
                  <X className="w-5 h-5 mt-0.5 flex-shrink-0 text-destructive" />
                  <div className="flex-1">
                    <p className="text-sm text-destructive">{error}</p>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setError(null)}
                      className="h-auto p-0 text-xs text-destructive hover:text-destructive underline mt-1"
                    >
                      Dismiss
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ) : loadingState.folders ? (
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-3 text-sm">
                  <div className="w-4 h-4 border-2 border-muted-foreground/30 border-t-muted-foreground rounded-full animate-spin"></div>
                  Loading tree...
                </div>
              </CardContent>
            </Card>
          ) : folderTree && rootFolder ? (
            <div className="space-y-1">
              <FolderTree
                key={rootFolder}
                node={{
                  name: rootFolder.split('\\').pop() || rootFolder,
                  path: rootFolder,
                  isDirectory: true,
                  children: folderTree
                }}
                onSelect={handleFolderSelect}
                selectedPath={selectedFolder || undefined}
                isRoot={true}
              />
            </div>
          ) : (
            <Card>
              <CardContent className="p-6 text-center">
                <Folder className="w-8 h-8 mx-auto mb-2 text-muted-foreground" />
                <p className="text-sm text-muted-foreground">Select a folder to view tree</p>
              </CardContent>
            </Card>
          )}
          </div>
        </ScrollArea>
      </aside>

      {/* Material Design Main Content */}
      <main className="flex-1 flex flex-col bg-background">
        {/* Material Design Header */}
        <header className="border-b bg-card shadow-sm">
          <div className="px-6 py-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-primary/10 rounded-lg">
                    <Search className="h-5 w-5 text-primary" />
                  </div>
                  <h1 className="text-2xl font-semibold">Photo Gallery</h1>
                </div>
                {selectedFolder && (
                  <Badge variant="outline" className="gap-2 px-3 py-1">
                    <FolderOpen className="h-3 w-3" />
                    <span className="truncate max-w-md">
                      {selectedFolder.split('\\').pop()}
                    </span>
                  </Badge>
                )}
              </div>
              <SettingsPopup />
            </div>
          </div>
          {/* Material Design Filters */}
          {allTags.length > 0 && (
            <div className="border-t bg-muted/30 px-6 py-4">
              <div className="space-y-4">

                {/* Material Design Search Input */}
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <Input
                    type="text"
                    value={tagSearchInput}
                    onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                      setTagSearchInput(e.target.value)
                    }
                    placeholder="Search tags..."
                    className="pl-10 bg-background border-border/50 focus:border-primary"
                  />
                </div>

                {/* Active Filters */}
                {searchTags.length > 0 && (
                  <div className="space-y-3">
                    <span className="text-xs font-medium text-muted-foreground uppercase tracking-wide">Active Filters</span>
                    <div className="flex flex-wrap gap-2">
                      {searchTags.map((tag) => (
                        <Badge
                          key={tag}
                          variant="default"
                          className="cursor-pointer hover:bg-destructive hover:text-destructive-foreground transition-all duration-200 gap-1.5 px-3 py-1"
                          onClick={() => {
                            const newSearchTags = searchTags.filter((t) => t !== tag)
                            setSearchTags(newSearchTags)
                            loadPhotos(selectedFolder || undefined, newSearchTags, true)
                          }}
                        >
                          {tag}
                          <X className="h-3 w-3" />
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}

                {/* Available Tags */}
                <div className="space-y-3">
                  <span className="text-xs font-medium text-muted-foreground uppercase tracking-wide">Available Tags</span>
                  <div className="max-h-32 overflow-y-auto">
                    <div className="flex flex-wrap gap-2 p-4 bg-card rounded-lg border shadow-sm">
                      {displayTags.map((tag) => (
                        <Badge
                          key={tag}
                          variant={searchTags.includes(tag) ? "default" : "secondary"}
                          className="cursor-pointer hover:shadow-sm transition-all duration-200 px-3 py-1"
                          onClick={() => {
                            console.log('New search tags: ', searchTags)
                            if (searchTags.includes(tag)) {
                              handleSearch(searchTags.filter((t) => t !== tag))
                            } else {
                              handleSearch([...searchTags, tag])
                            }
                          }}
                        >
                          {tag}
                        </Badge>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </header>

        {/* Material Design Photo Grid */}
        <div className="flex-1 overflow-hidden">
          <div className="h-full overflow-y-auto">
            <div className="p-6">
              {loadingState.photos ? (
                <div className="flex items-center justify-center h-64">
                  <Card className="shadow-lg">
                    <CardContent className="p-8 text-center">
                      <div className="w-12 h-12 border-3 border-primary/30 border-t-primary rounded-full animate-spin mx-auto mb-4"></div>
                      <p className="text-muted-foreground font-medium">Loading photos...</p>
                    </CardContent>
                  </Card>
                </div>
              ) : photos.length > 0 ? (
                <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 2xl:grid-cols-8 gap-6">
                    {photos.map((photo) => (
                      <Card
                        key={photo.id}
                        className="group cursor-pointer overflow-hidden border-0 shadow-md hover:shadow-xl transition-all duration-300 bg-card"
                        onClick={() => openPhotoView(photo)}
                      >
                        <div className="aspect-square relative overflow-hidden rounded-t-lg">
                          {photo.thumbnail_path ? (
                            <img
                              src={`thumbnail://${photo.thumbnail_path}`}
                              alt={photo.path}
                              className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-110"
                              onLoad={() => {
                                /* Thumbnail loaded successfully */
                              }}
                              onError={(e: React.SyntheticEvent<HTMLImageElement>) => {
                                console.error(
                                  'Failed to load thumbnail:',
                                  `thumbnail://${photo.thumbnail_path}`
                                )
                                ;(e.target as HTMLImageElement).style.display = 'none'
                              }}
                            />
                          ) : (
                            <div className="w-full h-full bg-gradient-to-br from-primary/5 to-primary/20 flex flex-col items-center justify-center">
                              <div className="animate-spin rounded-full h-8 w-8 border-3 border-primary/30 border-t-primary mb-3"></div>
                              <span className="text-xs text-muted-foreground font-medium">
                                Generating...
                              </span>
                            </div>
                          )}

                          {/* Material Design Hover Overlay */}
                          <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-all duration-300 flex items-end justify-center pb-4">
                            <div className="bg-white/90 backdrop-blur-sm rounded-full p-2">
                              <Search className="w-5 h-5 text-primary" />
                            </div>
                          </div>
                        </div>

                        <CardContent className="p-4">
                          <p className="text-xs text-muted-foreground text-center truncate font-medium">
                            {photo.path.split('\\').pop()}
                          </p>
                        </CardContent>
                      </Card>
                    ))}
                </div>

                {/* Material Design Load More */}
                {hasMorePhotos && !loadingState.photos && (
                  <div className="flex justify-center mt-12">
                    <Button
                      onClick={() => loadPhotos(selectedFolder || undefined, searchTags, false)}
                      disabled={loadingState.loadingMore}
                      size="lg"
                      className="gap-3 px-8 py-3 shadow-lg hover:shadow-xl transition-all duration-300"
                    >
                      {loadingState.loadingMore ? (
                        <>
                          <div className="w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                          Loading more photos...
                        </>
                      ) : (
                        <>
                          Load More Photos
                          <ChevronLeft className="w-5 h-5 rotate-90" />
                        </>
                      )}
                    </Button>
                  </div>
                )}
              </>) : (
                <div className="flex items-center justify-center h-96">
                  <Card className="shadow-xl border-0">
                    <CardContent className="p-12 text-center">
                      <div className="p-4 bg-primary/10 rounded-full w-fit mx-auto mb-6">
                        <Search className="w-12 h-12 text-primary" />
                      </div>
                      <CardTitle className="mb-3 text-xl">No photos found</CardTitle>
                      <p className="text-muted-foreground">Select a folder to scan or search by tags to get started</p>
                    </CardContent>
                  </Card>
                </div>
              )}
            </div>
          </div>
        </div>
      </main>

      {/* Modern Fullscreen Photo Viewer */}
        {isFullscreenViewer && selectedPhoto && (
          <div className="fixed inset-0 bg-black z-50 flex items-center justify-center">
            {/* Control Buttons */}
            <div className="absolute top-4 right-4 z-60 flex gap-2">
              {/* Toggle Tags Button */}
              <Button
                variant="secondary"
                size="icon"
                onClick={() => setShowTagsPanel(!showTagsPanel)}
                className="w-12 h-12 bg-black/50 hover:bg-black/70 text-white rounded-full backdrop-blur-sm border-0"
                title={showTagsPanel ? 'Hide tags' : 'Show tags'}
              >
                {showTagsPanel ? (
                  <Settings className="w-6 h-6" />
                ) : (
                  <Tag className="w-6 h-6" />
                )}
              </Button>

              {/* Open in Explorer Button */}
              <Button
                variant="secondary"
                size="icon"
                onClick={openInExplorer}
                className="w-12 h-12 bg-black/50 hover:bg-black/70 text-white rounded-full backdrop-blur-sm border-0"
                title="Open in Explorer"
              >
                <FolderOpen className="w-6 h-6" />
              </Button>

              {/* Close Button */}
              <Button
                variant="secondary"
                size="icon"
                onClick={closeFullscreenViewer}
                className="w-12 h-12 bg-black/50 hover:bg-black/70 text-white rounded-full backdrop-blur-sm border-0"
                title="Close"
              >
                <X className="w-6 h-6" />
              </Button>
            </div>

            {/* Navigation Arrows */}
            {currentPhotoIndex > 0 && (
              <Button
                variant="secondary"
                size="icon"
                onClick={() => navigatePhoto('prev')}
                className="absolute left-4 top-1/2 transform -translate-y-1/2 z-60 w-12 h-12 bg-black/50 hover:bg-black/70 text-white rounded-full backdrop-blur-sm border-0"
              >
                <ChevronLeft className="w-6 h-6" />
              </Button>
            )}

            {currentPhotoIndex < photos.length - 1 && (
              <Button
                variant="secondary"
                size="icon"
                onClick={() => navigatePhoto('next')}
                className="absolute right-4 top-1/2 transform -translate-y-1/2 z-60 w-12 h-12 bg-black/50 hover:bg-black/70 text-white rounded-full backdrop-blur-sm border-0"
              >
                <ChevronLeft className="w-6 h-6 rotate-180" />
              </Button>
            )}

            {/* Photo Counter */}
            <Badge
              variant="secondary"
              className="absolute top-4 left-4 z-60 bg-black/50 text-white backdrop-blur-sm border-0"
            >
              {currentPhotoIndex + 1} / {photos.length}
            </Badge>

            {/* Main Photo */}
            <div className="absolute inset-0 flex items-center justify-center">
              <img
                src={`file://${selectedPhoto.path.replace(/\\/g, '/')}`}
                alt={selectedPhoto.path}
                className="w-full h-full object-contain"
                onLoad={() => {
                  console.log('Photo loaded successfully:', selectedPhoto.path)
                }}
                onError={(e: React.SyntheticEvent<HTMLImageElement>) => {
                  console.error('Failed to load full image:', selectedPhoto.path)
                  console.error(
                    'Photo URL was:',
                    `file://${selectedPhoto.path.replace(/\\/g, '/')}`
                  )
                  // Fallback to thumbnail
                  if (selectedPhoto.thumbnail_path) {
                    ;(e.target as HTMLImageElement).src =
                      `thumbnail://${selectedPhoto.thumbnail_path}`
                  }
                }}
              />
            </div>

            {/* Info Panel */}
            <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-6 text-white">
              <div className="max-w-4xl mx-auto">
                <h3 className="text-lg font-semibold mb-2">
                  {selectedPhoto.path.split('\\').pop()}
                </h3>
                <p className="text-sm text-white/80 mb-4">
                  {new Date(selectedPhoto.date).toLocaleDateString()} • {selectedPhoto.path}
                </p>

                {/* Tags Panel - Conditionally displayed */}
                {showTagsPanel && (
                  <>
                    {/* Tags */}
                    <div className="flex flex-wrap gap-2 mb-6">
                      {photoTags.map((tag) => (
                        <span
                          key={tag}
                          className="group bg-white/20 backdrop-blur-sm text-white px-3 py-1.5 rounded-full text-sm hover:bg-red-500/80 cursor-pointer transition-all flex items-center gap-2"
                          onClick={() => removeTag(tag)}
                        >
                          {tag}
                          <svg
                            className="w-3 h-3 opacity-60 group-hover:opacity-100"
                            fill="currentColor"
                            viewBox="0 0 20 20"
                          >
                            <path
                              fillRule="evenodd"
                              d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                              clipRule="evenodd"
                            />
                          </svg>
                        </span>
                      ))}
                    </div>

                    {/* Add Tag Controls */}
                    <div className="space-y-3">
                      {/* Existing Tags */}
                      {allTags.filter((tag) => !photoTags.includes(tag)).length > 0 && (
                        <div>
                          <p className="text-white/80 text-sm mb-2">Add existing tag:</p>
                          <div className="flex flex-wrap gap-2">
                            {allTags
                              .filter((tag) => !photoTags.includes(tag))
                              .slice(0, 8)
                              .map((tag) => (
                                <button
                                  key={tag}
                                  onClick={() => addTagInstantly(tag)}
                                  className="bg-white/10 hover:bg-white/20 text-white px-3 py-1.5 rounded-full text-sm transition-all border border-white/20 hover:border-white/40"
                                >
                                  + {tag}
                                </button>
                              ))}
                          </div>
                        </div>
                      )}

                      {/* Create New Tag with Autocomplete */}
                      <div>
                        <p className="text-white/80 text-sm mb-2">Add or create tag:</p>
                        <div className="flex gap-2">
                          <div className="flex-1 relative">
                            <input
                              type="text"
                              value={newTag}
                              onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                                handleTagInputChange(e.target.value)
                              }
                              placeholder="Type to search or add new tag..."
                              className="w-full bg-white/20 backdrop-blur-sm text-white placeholder-white/60 border border-white/30 rounded-lg px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-white/50"
                              onKeyPress={(e) => {
                                if (e.key === 'Enter' && newTag.trim()) {
                                  addTag()
                                }
                              }}
                              onFocus={() => filterTags(newTag)}
                              onBlur={() => {
                                // Delay hiding suggestions to allow clicking on them
                                setTimeout(() => setShowTagSuggestions(false), 200)
                              }}
                            />

                            {/* Tag Suggestions Dropdown - Smart positioning */}
                            {showTagSuggestions && filteredTags.length > 0 && (
                              <div className="absolute bottom-full left-0 right-0 mb-1 bg-white/95 backdrop-blur-sm border border-white/30 rounded-lg shadow-lg z-50 max-h-48 overflow-y-auto">
                                {filteredTags.map((tag) => (
                                  <button
                                    key={tag}
                                    onClick={() => {
                                      addTagInstantly(tag)
                                      setNewTag('')
                                      setShowTagSuggestions(false)
                                    }}
                                    className="w-full text-left px-3 py-2 text-sm text-gray-800 hover:bg-blue-100 transition-colors flex items-center gap-2"
                                  >
                                    <svg
                                      className="w-3 h-3 text-blue-600"
                                      fill="none"
                                      stroke="currentColor"
                                      viewBox="0 0 24 24"
                                    >
                                      <path
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                        strokeWidth={2}
                                        d="M12 6v6m0 0v6m0-6h6m-6 0H6"
                                      />
                                    </svg>
                                    {tag}
                                  </button>
                                ))}
                              </div>
                            )}
                          </div>
                          <button
                            onClick={addTag}
                            disabled={!newTag.trim()}
                            className="bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 disabled:opacity-50 text-white px-4 py-2 rounded-lg text-sm transition-all flex items-center gap-2"
                          >
                            <svg
                              className="w-4 h-4"
                              fill="none"
                              stroke="currentColor"
                              viewBox="0 0 24 24"
                            >
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M12 6v6m0 0v6m0-6h6m-6 0H6"
                              />
                            </svg>
                            Add
                          </button>
                        </div>
                      </div>
                    </div>
                  </>
                )}
              </div>
            </div>
          </div>
        )}
    </div>
  )
}

export default App
