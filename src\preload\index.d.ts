import { ElectronAPI } from '@electron-toolkit/preload'

interface EnhancedElectronAPI extends ElectronAPI {
  ipcRenderer: {
    invoke: (channel: string, ...args: unknown[]) => Promise<unknown>
    on: (channel: string, func: (...args: unknown[]) => void) => () => void
    once: (channel: string, func: (...args: unknown[]) => void) => void
  }
}

declare global {
  interface Window {
    electron: EnhancedElectronAPI
    api: unknown
  }
}
