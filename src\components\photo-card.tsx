import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"

export interface Photo {
  id: number;
  title: string;
  url: string;
  tags: string[];
}

interface PhotoCardProps {
  photo: Photo;
}

export function PhotoCard({ photo }: PhotoCardProps): JSX.Element {
  return (
    <Card>
      <CardHeader>
        <CardTitle>{photo.title}</CardTitle>
        <CardDescription>ID: {photo.id}</CardDescription>
      </CardHeader>
      <CardContent>
        <img src={photo.url} alt={photo.title} className="rounded-md aspect-video object-cover" />
        <div className="flex flex-wrap gap-2 mt-4">
          {photo.tags.map((tag) => <Badge key={tag} variant="secondary">{tag}</Badge>)}
        </div>
      </CardContent>
      <CardFooter className="flex justify-end gap-2">
        <Button variant="outline">Edit Tags</Button>
        <Button variant="destructive">Delete</Button>
      </CardFooter>
    </Card>
  )
}