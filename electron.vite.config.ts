import { resolve } from 'path'
import { defineConfig, externalizeDepsPlugin } from 'electron-vite'
import react from '@vitejs/plugin-react'
import tailwindcss from '@tailwindcss/vite'

export default defineConfig({
  main: {
    plugins: [externalizeDepsPlugin(), tailwindcss()]
  },
  preload: {
    plugins: [externalizeDepsPlugin(), tailwindcss()]
  },
  renderer: {
    resolve: {
      alias: {
        '@renderer': resolve('src/renderer/src'),
        '@': resolve('src')
      }
    },
    plugins: [
      react({
        include: '**/*.{jsx,tsx}',
        babel: {
          plugins: []
        }
      }),
      tailwindcss()
    ]
  }
})
