import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, Folder<PERSON><PERSON> } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { ThemeToggle } from "@/components/theme-toggle"
import { useState, useEffect, useRef } from "react"

interface SettingsPopupProps {
  onDefaultFolderChange?: (folder: string | null) => void
}

export function SettingsPopup({ onDefaultFolderChange }: SettingsPopupProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [defaultFolder, setDefaultFolder] = useState<string | null>(null)
  const modalRef = useRef<HTMLDivElement>(null)

  // Load default folder from localStorage on mount
  useEffect(() => {
    const saved = localStorage.getItem('photoTagManager_defaultFolder')
    if (saved) {
      setDefaultFolder(saved)
      onDefaultFolderChange?.(saved)
    }
  }, [onDefaultFolderChange])

  // Save default folder to localStorage
  const saveDefaultFolder = (folder: string | null) => {
    if (folder) {
      localStorage.setItem('photoTagManager_defaultFolder', folder)
    } else {
      localStorage.removeItem('photoTagManager_defaultFolder')
    }
    setDefaultFolder(folder)
    onDefaultFolderChange?.(folder)
  }

  // Select default folder
  const selectDefaultFolder = async () => {
    try {
      const folderPath = await window.electron.ipcRenderer.invoke('select-folder')
      if (folderPath) {
        saveDefaultFolder(folderPath)
      }
    } catch (error) {
      console.error('Error selecting default folder:', error)
    }
  }

  // Clear default folder
  const clearDefaultFolder = () => {
    saveDefaultFolder(null)
  }

  // Close modal when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (modalRef.current && !modalRef.current.contains(event.target as Node)) {
        setIsOpen(false)
      }
    }

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside)
      return () => document.removeEventListener('mousedown', handleClickOutside)
    }

    return () => {} // Return empty cleanup function when not open
  }, [isOpen])

  // Close modal on Escape key
  useEffect(() => {
    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        setIsOpen(false)
      }
    }

    if (isOpen) {
      document.addEventListener('keydown', handleEscape)
      return () => document.removeEventListener('keydown', handleEscape)
    }

    return () => {} // Return empty cleanup function when not open
  }, [isOpen])

  return (
    <>
      <Button
        variant="ghost"
        size="icon"
        className="h-8 w-8"
        title="Settings"
        onClick={() => setIsOpen(true)}
      >
        <Settings className="h-4 w-4" />
      </Button>

      {isOpen && (
        <div className="fixed inset-0 z-50">
          {/* Backdrop */}
          <div
            className="absolute inset-0 bg-black/50 backdrop-blur-sm"
            onClick={() => setIsOpen(false)}
          />

          {/* Modal Content */}
          <div className="relative z-10 flex items-center justify-center min-h-full p-4">
            <div
              ref={modalRef}
              className="w-full max-w-lg bg-white dark:bg-gray-900 rounded-lg shadow-2xl border border-gray-200 dark:border-gray-700"
            >
              {/* Header */}
              <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
                <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                  Settings
                </h2>
                <button
                  onClick={() => setIsOpen(false)}
                  className="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-md transition-colors"
                >
                  <X className="h-4 w-4" />
                </button>
              </div>

              {/* Content */}
              <div className="p-6 space-y-6">
                {/* Default Folder Setting */}
                <div className="space-y-3">
                  <div className="space-y-1">
                    <p className="text-sm font-medium text-gray-900 dark:text-gray-100">
                      Default Folder
                    </p>
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      Set a folder to automatically load when the app starts
                    </p>
                  </div>

                  {defaultFolder ? (
                    <div className="space-y-2">
                      <div className="flex items-center gap-2 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
                        <FolderOpen className="h-4 w-4 text-blue-600 dark:text-blue-400 flex-shrink-0" />
                        <span className="text-sm text-gray-900 dark:text-gray-100 truncate flex-1">
                          {defaultFolder.split('\\').pop()}
                        </span>
                      </div>
                      <div className="flex gap-2">
                        <button
                          onClick={selectDefaultFolder}
                          className="flex-1 px-3 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors flex items-center justify-center gap-2"
                        >
                          <Folder className="h-4 w-4" />
                          Change
                        </button>
                        <button
                          onClick={clearDefaultFolder}
                          className="px-3 py-2 text-sm font-medium text-red-600 dark:text-red-400 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-red-50 dark:hover:bg-red-900/20 transition-colors"
                        >
                          Clear
                        </button>
                      </div>
                    </div>
                  ) : (
                    <button
                      onClick={selectDefaultFolder}
                      className="w-full px-3 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors flex items-center justify-start gap-2"
                    >
                      <Folder className="h-4 w-4" />
                      Select Default Folder
                    </button>
                  )}
                </div>

                {/* Theme Setting */}
                <div className="flex items-center justify-between pt-3 border-t border-gray-200 dark:border-gray-700">
                  <div className="space-y-1">
                    <p className="text-sm font-medium text-gray-900 dark:text-gray-100">
                      Theme
                    </p>
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      Switch between light and dark mode
                    </p>
                  </div>
                  <ThemeToggle />
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  )
}
