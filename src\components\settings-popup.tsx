import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, Folder<PERSON><PERSON> } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { ThemeToggle } from "@/components/theme-toggle"
import { useState, useEffect, useRef } from "react"

interface SettingsPopupProps {
  onDefaultFolderChange?: (folder: string | null) => void
}

export function SettingsPopup({ onDefaultFolderChange }: SettingsPopupProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [defaultFolder, setDefaultFolder] = useState<string | null>(null)
  const modalRef = useRef<HTMLDivElement>(null)

  // Load default folder from localStorage on mount
  useEffect(() => {
    const saved = localStorage.getItem('photoTagManager_defaultFolder')
    if (saved) {
      setDefaultFolder(saved)
      onDefaultFolderChange?.(saved)
    }
  }, [onDefaultFolderChange])

  // Save default folder to localStorage
  const saveDefaultFolder = (folder: string | null) => {
    if (folder) {
      localStorage.setItem('photoTagManager_defaultFolder', folder)
    } else {
      localStorage.removeItem('photoTagManager_defaultFolder')
    }
    setDefaultFolder(folder)
    onDefaultFolderChange?.(folder)
  }

  // Select default folder
  const selectDefaultFolder = async () => {
    try {
      const folderPath = await window.electron.ipcRenderer.invoke('select-folder')
      if (folderPath) {
        saveDefaultFolder(folderPath)
      }
    } catch (error) {
      console.error('Error selecting default folder:', error)
    }
  }

  // Clear default folder
  const clearDefaultFolder = () => {
    saveDefaultFolder(null)
  }

  // Close modal when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (modalRef.current && !modalRef.current.contains(event.target as Node)) {
        setIsOpen(false)
      }
    }

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside)
      return () => document.removeEventListener('mousedown', handleClickOutside)
    }

    return () => {} // Return empty cleanup function when not open
  }, [isOpen])

  // Close modal on Escape key
  useEffect(() => {
    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        setIsOpen(false)
      }
    }

    if (isOpen) {
      document.addEventListener('keydown', handleEscape)
      return () => document.removeEventListener('keydown', handleEscape)
    }

    return () => {} // Return empty cleanup function when not open
  }, [isOpen])

  return (
    <>
      <Button
        variant="ghost"
        size="icon"
        className="h-8 w-8"
        title="Settings"
        onClick={() => setIsOpen(true)}
      >
        <Settings className="h-4 w-4" />
      </Button>

      {isOpen && (
        <div
          className="fixed inset-0 z-50 flex items-center justify-center"
          style={{ backgroundColor: 'rgba(0, 0, 0, 0.5)' }}
        >
          <Card
            ref={modalRef}
            className="w-full max-w-lg mx-4 shadow-2xl bg-background border"
          >
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4 border-b">
              <CardTitle className="text-lg font-semibold text-foreground">Settings</CardTitle>
              <Button
                variant="ghost"
                size="icon"
                className="h-8 w-8 hover:bg-muted text-foreground"
                onClick={() => setIsOpen(false)}
              >
                <X className="h-4 w-4" />
              </Button>
            </CardHeader>
            <CardContent className="space-y-6 p-6">
              {/* Default Folder Setting */}
              <div className="space-y-3">
                <div className="space-y-1">
                  <p className="text-sm font-medium text-foreground">Default Folder</p>
                  <p className="text-xs text-muted-foreground">
                    Set a folder to automatically load when the app starts
                  </p>
                </div>

                {defaultFolder ? (
                  <div className="space-y-2">
                    <div className="flex items-center gap-2 p-3 bg-muted/50 rounded-lg border">
                      <FolderOpen className="h-4 w-4 text-primary flex-shrink-0" />
                      <span className="text-sm text-foreground truncate flex-1">
                        {defaultFolder.split('\\').pop()}
                      </span>
                    </div>
                    <div className="flex gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={selectDefaultFolder}
                        className="flex-1"
                      >
                        <Folder className="h-4 w-4 mr-2" />
                        Change
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={clearDefaultFolder}
                        className="text-destructive hover:text-destructive"
                      >
                        Clear
                      </Button>
                    </div>
                  </div>
                ) : (
                  <Button
                    variant="outline"
                    onClick={selectDefaultFolder}
                    className="w-full justify-start"
                  >
                    <Folder className="h-4 w-4 mr-2" />
                    Select Default Folder
                  </Button>
                )}
              </div>

              {/* Theme Setting */}
              <div className="flex items-center justify-between pt-3 border-t">
                <div className="space-y-1">
                  <p className="text-sm font-medium text-foreground">Theme</p>
                  <p className="text-xs text-muted-foreground">
                    Switch between light and dark mode
                  </p>
                </div>
                <ThemeToggle />
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </>
  )
}
