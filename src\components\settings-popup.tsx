import { Setting<PERSON>, <PERSON>, Folder, FolderOpen } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { ThemeToggle } from '@/components/theme-toggle'
import { useState, useEffect, useRef } from 'react'

interface SettingsPopupProps {
  onDefaultFolderChange?: (folder: string | null) => void
}

export function SettingsPopup({ onDefaultFolderChange }: SettingsPopupProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [defaultFolder, setDefaultFolder] = useState<string | null>(null)
  const [isDarkMode, setIsDarkMode] = useState(false)
  const modalRef = useRef<HTMLDivElement>(null)

  // Check for dark mode
  useEffect(() => {
    const checkDarkMode = () => {
      setIsDarkMode(document.documentElement.classList.contains('dark'))
    }

    checkDarkMode()

    // Watch for theme changes
    const observer = new MutationObserver(checkDarkMode)
    observer.observe(document.documentElement, {
      attributes: true,
      attributeFilter: ['class']
    })

    return () => observer.disconnect()
  }, [])

  // Load default folder from localStorage on mount
  useEffect(() => {
    const saved = localStorage.getItem('photoTagManager_defaultFolder')
    if (saved) {
      setDefaultFolder(saved)
      onDefaultFolderChange?.(saved)
    }
  }, [onDefaultFolderChange])

  // Save default folder to localStorage
  const saveDefaultFolder = (folder: string | null) => {
    if (folder) {
      localStorage.setItem('photoTagManager_defaultFolder', folder)
    } else {
      localStorage.removeItem('photoTagManager_defaultFolder')
    }
    setDefaultFolder(folder)
    onDefaultFolderChange?.(folder)
  }

  // Select default folder
  const selectDefaultFolder = async () => {
    try {
      const folderPath = await window.electron.ipcRenderer.invoke('select-folder')
      if (folderPath) {
        saveDefaultFolder(folderPath)
      }
    } catch (error) {
      console.error('Error selecting default folder:', error)
    }
  }

  // Clear default folder
  const clearDefaultFolder = () => {
    saveDefaultFolder(null)
  }

  // Close modal when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (modalRef.current && !modalRef.current.contains(event.target as Node)) {
        setIsOpen(false)
      }
    }

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside)
      return () => document.removeEventListener('mousedown', handleClickOutside)
    }

    return () => {} // Return empty cleanup function when not open
  }, [isOpen])

  // Close modal on Escape key
  useEffect(() => {
    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        setIsOpen(false)
      }
    }

    if (isOpen) {
      document.addEventListener('keydown', handleEscape)
      return () => document.removeEventListener('keydown', handleEscape)
    }

    return () => {} // Return empty cleanup function when not open
  }, [isOpen])

  return (
    <>
      <Button
        variant="ghost"
        size="icon"
        className="h-8 w-8"
        title="Settings"
        onClick={() => setIsOpen(true)}
      >
        <Settings className="h-4 w-4" />
      </Button>

      {isOpen && (
        <div
          className="fixed inset-0 z-50"
          style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            backgroundColor: 'rgba(0, 0, 0, 0.5)',
            backdropFilter: 'blur(2px)'
          }}
          onClick={() => setIsOpen(false)}
        >
          {/* Modal Content */}
          <div
            ref={modalRef}
            className="rounded-lg shadow-2xl border relative mx-4"
            style={{
              backgroundColor: isDarkMode ? '#1f2937' : 'white',
              borderColor: isDarkMode ? '#374151' : '#e5e7eb',
              maxHeight: '90vh',
              maxWidth: '50%',
              width: '100%',
              overflow: 'auto'
            }}
            onClick={(e) => e.stopPropagation()}
          >
            {/* Header */}
            <div
              className="flex items-center justify-between p-6 border-b"
              style={{ borderColor: isDarkMode ? '#374151' : '#e5e7eb' }}
            >
              <h2
                className="text-lg font-semibold"
                style={{ color: isDarkMode ? '#f9fafb' : '#111827' }}
              >
                Settings
              </h2>
              <button
                onClick={() => setIsOpen(false)}
                className="p-2 rounded-md transition-colors"
                style={{
                  color: isDarkMode ? '#9ca3af' : '#6b7280',
                  backgroundColor: 'transparent'
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.backgroundColor = isDarkMode ? '#374151' : '#f3f4f6'
                  e.currentTarget.style.color = isDarkMode ? '#d1d5db' : '#4b5563'
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.backgroundColor = 'transparent'
                  e.currentTarget.style.color = isDarkMode ? '#9ca3af' : '#6b7280'
                }}
              >
                <X className="h-4 w-4" />
              </button>
            </div>

            {/* Content */}
            <div className="p-6 space-y-6">
              {/* Default Folder Setting */}
              <div className="space-y-3">
                <div className="space-y-1">
                  <p
                    className="text-sm font-medium"
                    style={{ color: isDarkMode ? '#f9fafb' : '#111827' }}
                  >
                    Default Folder
                  </p>
                  <p
                    className="text-xs"
                    style={{ color: isDarkMode ? '#9ca3af' : '#6b7280' }}
                  >
                    Set a folder to automatically load when the app starts
                  </p>
                </div>

                {defaultFolder ? (
                  <div className="space-y-2">
                    <div
                      className="flex items-center gap-2 p-3 rounded-lg border"
                      style={{
                        backgroundColor: isDarkMode ? '#374151' : '#f9fafb',
                        borderColor: isDarkMode ? '#4b5563' : '#e5e7eb'
                      }}
                    >
                      <FolderOpen
                        className="h-4 w-4 flex-shrink-0"
                        style={{ color: isDarkMode ? '#60a5fa' : '#2563eb' }}
                      />
                      <span
                        className="text-sm truncate flex-1"
                        style={{ color: isDarkMode ? '#f9fafb' : '#111827' }}
                      >
                        {defaultFolder.split('\\').pop()}
                      </span>
                    </div>
                    <div className="flex gap-2">
                      <button
                        onClick={selectDefaultFolder}
                        className="flex-1 px-3 py-2 text-sm font-medium rounded-md transition-colors flex items-center justify-center gap-2"
                        style={{
                          color: isDarkMode ? '#d1d5db' : '#374151',
                          backgroundColor: isDarkMode ? '#374151' : 'white',
                          borderColor: isDarkMode ? '#4b5563' : '#d1d5db',
                          border: '1px solid'
                        }}
                        onMouseEnter={(e) => {
                          e.currentTarget.style.backgroundColor = isDarkMode ? '#4b5563' : '#f9fafb'
                        }}
                        onMouseLeave={(e) => {
                          e.currentTarget.style.backgroundColor = isDarkMode ? '#374151' : 'white'
                        }}
                      >
                        <Folder className="h-4 w-4" />
                        Change
                      </button>
                      <button
                        onClick={clearDefaultFolder}
                        className="px-3 py-2 text-sm font-medium rounded-md transition-colors"
                        style={{
                          color: isDarkMode ? '#f87171' : '#dc2626',
                          backgroundColor: isDarkMode ? '#374151' : 'white',
                          borderColor: isDarkMode ? '#4b5563' : '#d1d5db',
                          border: '1px solid'
                        }}
                        onMouseEnter={(e) => {
                          e.currentTarget.style.backgroundColor = isDarkMode ? '#7f1d1d' : '#fef2f2'
                        }}
                        onMouseLeave={(e) => {
                          e.currentTarget.style.backgroundColor = isDarkMode ? '#374151' : 'white'
                        }}
                      >
                        Clear
                      </button>
                    </div>
                  </div>
                ) : (
                  <button
                    onClick={selectDefaultFolder}
                    className="w-full px-3 py-2 text-sm font-medium rounded-md transition-colors flex items-center justify-start gap-2"
                    style={{
                      color: isDarkMode ? '#d1d5db' : '#374151',
                      backgroundColor: isDarkMode ? '#374151' : 'white',
                      borderColor: isDarkMode ? '#4b5563' : '#d1d5db',
                      border: '1px solid'
                    }}
                    onMouseEnter={(e) => {
                      e.currentTarget.style.backgroundColor = isDarkMode ? '#4b5563' : '#f9fafb'
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.style.backgroundColor = isDarkMode ? '#374151' : 'white'
                    }}
                  >
                    <Folder className="h-4 w-4" />
                    Select Default Folder
                  </button>
                )}
                </div>

              {/* Theme Setting */}
              <div
                className="flex items-center justify-between pt-3 border-t"
                style={{ borderColor: isDarkMode ? '#374151' : '#e5e7eb' }}
              >
                <div className="space-y-1">
                  <p
                    className="text-sm font-medium"
                    style={{ color: isDarkMode ? '#f9fafb' : '#111827' }}
                  >
                    Theme
                  </p>
                  <p
                    className="text-xs"
                    style={{ color: isDarkMode ? '#9ca3af' : '#6b7280' }}
                  >
                    Switch between light and dark mode
                  </p>
                </div>
                <ThemeToggle />
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  )
}
