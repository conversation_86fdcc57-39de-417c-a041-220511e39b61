import React, { useState, useEffect } from 'react'
import { ChevronRight, Folder, FolderOpen } from 'lucide-react'

interface FolderNode {
  name: string
  path: string
  isDirectory: boolean
  children?: FolderNode[]
}

interface FolderTreeProps {
  node: FolderNode
  onSelect: (path: string) => void
  selectedPath?: string
  isRoot?: boolean
  depth?: number
}

const FolderTree: React.FC<FolderTreeProps> = ({
  node,
  onSelect,
  selectedPath,
  isRoot = false,
  depth = 0
}) => {
  const [expanded, setExpanded] = useState(isRoot)
  const hasChildren = node.children && node.children.length > 0
  const isSelected = selectedPath === node.path
  const isParentOfSelected =
    selectedPath?.startsWith(node.path + '\\') || selectedPath?.startsWith(node.path + '/')

  // Auto-expand if this folder is a parent of the selected path
  useEffect(() => {
    if (isParentOfSelected || isSelected) {
      setExpanded(true)
    }
  }, [selectedPath, isParentOfSelected, isSelected])

  const handleToggle = (e: React.MouseEvent): void => {
    e.stopPropagation()
    setExpanded(!expanded)
  }

  const handleSelect = (): void => {
    onSelect(node.path)
  }

  return (
    <div className="select-none">
      {/* Windows Explorer Style Folder Row */}
      <div
        className={`
          folder-tree-item flex items-center cursor-pointer transition-colors
          ${isSelected ? 'selected' : ''}
        `}
        style={{ paddingLeft: `${depth * 16 + 4}px` }}
        onClick={handleSelect}
      >
        {/* Expand/Collapse Arrow */}
        <div className="w-4 h-4 flex items-center justify-center mr-1">
          {hasChildren && (
            <button
              onClick={(e) => {
                e.stopPropagation()
                handleToggle(e)
              }}
              className="w-4 h-4 flex items-center justify-center hover:bg-accent/70 transition-colors rounded-sm"
            >
              <ChevronRight
                className={`w-3 h-3 text-muted-foreground transition-transform duration-100 ${
                  expanded ? 'rotate-90' : ''
                }`}
              />
            </button>
          )}
        </div>

        {/* Folder Icon */}
        <div className="w-5 h-5 flex items-center justify-center mr-2">
          {expanded && hasChildren ? (
            <FolderOpen className="w-4 h-4 text-foreground" />
          ) : (
            <Folder className="w-4 h-4 text-foreground" />
          )}
        </div>

        {/* Folder Name */}
        <span className="truncate flex-1 leading-none">
          {node.name}
        </span>
      </div>

      {/* Children */}
      {expanded && hasChildren && (
        <div>
          {node.children!.map((childNode, index) => (
            <FolderTree
              key={index}
              node={childNode}
              onSelect={onSelect}
              selectedPath={selectedPath}
              depth={depth + 1}
            />
          ))}
        </div>
      )}
    </div>
  )
}

export default FolderTree
