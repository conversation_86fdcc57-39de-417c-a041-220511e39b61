import React, { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible'
import { ChevronRight, Folder, FolderOpen } from 'lucide-react'
import { cn } from '@/lib/utils'

interface FolderNode {
  name: string
  path: string
  isDirectory: boolean
  children?: FolderNode[]
}

interface FolderTreeProps {
  node: FolderNode
  onSelect: (path: string) => void
  selectedPath?: string
  isRoot?: boolean
  depth?: number
}

const FolderTree: React.FC<FolderTreeProps> = ({
  node,
  onSelect,
  selectedPath,
  isRoot = false,
  depth = 0
}) => {
  const [expanded, setExpanded] = useState(isRoot)
  const hasChildren = node.children && node.children.length > 0
  const isSelected = selectedPath === node.path
  const isParentOfSelected =
    selectedPath?.startsWith(node.path + '\\') || selectedPath?.startsWith(node.path + '/')

  // Auto-expand if this folder is a parent of the selected path
  useEffect(() => {
    if (isParentOfSelected || isSelected) {
      setExpanded(true)
    }
  }, [selectedPath, isParentOfSelected, isSelected])

  const handleToggle = (e: React.MouseEvent): void => {
    e.stopPropagation()
    setExpanded(!expanded)
  }

  const handleSelect = (): void => {
    onSelect(node.path)
  }

  return (
    <Collapsible open={expanded} onOpenChange={setExpanded}>
      <div className="select-none">
        <Button
          variant={isSelected ? "secondary" : "ghost"}
          className={cn(
            "w-full justify-start h-auto py-1.5 px-2 font-normal",
            isSelected && "bg-accent text-accent-foreground"
          )}
          style={{ paddingLeft: `${depth * 20 + 8}px` }}
          onClick={handleSelect}
        >
          {hasChildren && (
            <CollapsibleTrigger asChild>
              <div
                className="mr-1 w-4 h-4 flex items-center justify-center hover:bg-accent rounded transition-colors"
                onClick={handleToggle}
              >
                <ChevronRight
                  className={cn(
                    "w-3 h-3 text-muted-foreground transition-transform duration-200",
                    expanded && "rotate-90"
                  )}
                />
              </div>
            </CollapsibleTrigger>
          )}

          {expanded && hasChildren ? (
            <FolderOpen className="w-4 h-4 mr-2 flex-shrink-0 text-primary" />
          ) : (
            <Folder className="w-4 h-4 mr-2 flex-shrink-0 text-muted-foreground" />
          )}

          <span className={cn("text-sm truncate", isSelected && "font-medium")}>
            {node.name}
          </span>
        </Button>

        <CollapsibleContent>
          {hasChildren && (
            <div>
              {node.children!.map((childNode, index) => (
                <FolderTree
                  key={index}
                  node={childNode}
                  onSelect={onSelect}
                  selectedPath={selectedPath}
                  depth={depth + 1}
                />
              ))}
            </div>
          )}
        </CollapsibleContent>
      </div>
    </Collapsible>
  )
}

export default FolderTree
