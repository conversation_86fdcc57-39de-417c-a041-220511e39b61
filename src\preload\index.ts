import { contextBridge, ipc<PERSON><PERSON><PERSON> } from 'electron'
import { electronAPI, ElectronAPI } from '@electron-toolkit/preload'

declare global {
  interface Window {
    electron: ElectronAPI;
    api: Record<string, unknown>;
  }
}

// Custom APIs for renderer
const api: Record<string, unknown> = {}

// Use `contextBridge` to expose `electronAPI` and `api` to the renderer if the context isolation is enabled.
// Otherwise, just directly attach these to the DOM global object (window)
if (process.contextIsolated) {
  contextBridge.exposeInMainWorld('electron', electronAPI)
  contextBridge.exposeInMainWorld('api', api)
} else {
  window.electron = electronAPI
  window.api = api
}