import { app, BrowserWindow, dialog, ipcMain, protocol, shell } from 'electron'
import { join } from 'path'
import { electronApp, optimizer, is } from '@electron-toolkit/utils'
import icon from '../../resources/icon.png?asset'
import sqlite3 from 'sqlite3'
import { existsSync, mkdirSync } from 'fs'
import { readdir, stat as statAsync, unlink } from 'fs/promises'
import { join as pathJoin, extname, normalize } from 'path'
import sharp from 'sharp'

interface Photo {
  id: number
  path: string
  date: string
  thumbnail_path?: string
}

interface FolderNode {
  name: string
  path: string
  isDirectory: boolean
  children?: FolderNode[]
}

// Global main window reference
let mainWindow: BrowserWindow | null = null

// Database and thumbnails directory setup
const dbPath: string = pathJoin(app.getPath('userData'), 'photos.db')
const db: sqlite3.Database = new sqlite3.Database(dbPath)
const thumbnailsDir: string = pathJoin(app.getPath('userData'), 'thumbnails')

if (!existsSync(thumbnailsDir)) {
  mkdirSync(thumbnailsDir, { recursive: true })
}

const createTables = (): void => {
  db.serialize(() => {
    db.run('PRAGMA foreign_keys = ON')
    db.run(`
      CREATE TABLE IF NOT EXISTS settings (
        key TEXT PRIMARY KEY,
        value TEXT
      )
    `)
    db.run(`
      CREATE TABLE IF NOT EXISTS photos (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        path TEXT UNIQUE,
        date TEXT,
        thumbnail_path TEXT
      )
    `)
    db.run(`
      CREATE TABLE IF NOT EXISTS tags (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT UNIQUE
      )
    `)
    db.run(`
      CREATE TABLE IF NOT EXISTS photo_tags (
        photo_id INTEGER,
        tag_id INTEGER,
        PRIMARY KEY (photo_id, tag_id),
        FOREIGN KEY (photo_id) REFERENCES photos(id) ON DELETE CASCADE,
        FOREIGN KEY (tag_id) REFERENCES tags(id) ON DELETE CASCADE
      )
    `)
  })
}

function createWindow(): void {
  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    show: false,
    autoHideMenuBar: true,
    ...(process.platform === 'linux' ? { icon } : {}),
    webPreferences: {
      preload: join(__dirname, '../preload/index.js'),
      sandbox: false,
      webSecurity: false, // Temporarily disable for file access
      contextIsolation: true,
      nodeIntegration: false
    }
  })

  // Add CSP header for thumbnail protocol
  mainWindow.webContents.session.webRequest.onHeadersReceived((details, callback) => {
    callback({
      responseHeaders: {
        ...details.responseHeaders,
        'Content-Security-Policy': [
          "default-src 'self'; script-src 'self' 'unsafe-eval' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data: thumbnail: photo: file:; connect-src 'self' ws: wss:"
        ]
      }
    })
  })

  mainWindow.once('ready-to-show', () => {
    mainWindow?.show()
  })

  mainWindow.webContents.setWindowOpenHandler((details) => {
    shell.openExternal(details.url)
    return { action: 'deny' }
  })

  if (mainWindow) {
    // HMR for renderer base on electron-vite cli.
    // Load the remote URL for development or the local html file for production.
    if (mainWindow) {
      if (is.dev && process.env['ELECTRON_RENDERER_URL']) {
        mainWindow.loadURL(process.env['ELECTRON_RENDERER_URL'])
      } else {
        mainWindow.loadFile(join(__dirname, '../renderer/index.html'))
      }
    }
  }
}

// Register custom protocol scheme before app is ready
protocol.registerSchemesAsPrivileged([
  {
    scheme: 'thumbnail',
    privileges: {
      secure: true,
      standard: true,
      supportFetchAPI: true,
      corsEnabled: true
    }
  },
  {
    scheme: 'photo',
    privileges: {
      secure: true,
      standard: true,
      supportFetchAPI: true,
      corsEnabled: true
    }
  }
])

app.whenReady().then(async () => {
  // Register custom protocol for serving thumbnails
  const thumbnailResult = protocol.registerFileProtocol('thumbnail', (request, callback) => {
    const url: string = request.url.substr(12) // Remove 'thumbnail://'
    const filePath: string = pathJoin(thumbnailsDir, url)
    console.log('Thumbnail request:', request.url)
    console.log('Serving thumbnail at:', filePath)
    console.log('File exists:', existsSync(filePath))
    callback({ path: filePath })
  })
  console.log('Thumbnail protocol registration result:', thumbnailResult)

  // Register custom protocol for serving original photos
  const photoResult = protocol.registerFileProtocol('photo', (request, callback) => {
    console.log('=== PHOTO PROTOCOL CALLED ===')
    console.log('Raw request URL:', request.url)

    const url: string = request.url.substr(8) // Remove 'photo://'
    console.log('URL after removing protocol:', url)

    let filePath: string = decodeURIComponent(url)
    console.log('Decoded file path:', filePath)

    // Handle Windows paths properly
    if (process.platform === 'win32' && filePath.startsWith('/')) {
      filePath = filePath.substr(1) // Remove leading slash for Windows
      console.log('Removed leading slash for Windows:', filePath)
    }

    console.log('Final file path:', filePath)
    console.log('File exists:', existsSync(filePath))
    console.log('=== END PHOTO PROTOCOL ===')

    callback({ path: filePath })
  })
  console.log('Photo protocol registration result:', photoResult)

  createTables()

  electronApp.setAppUserModelId('com.electron')

  app.on('browser-window-created', (_, window) => {
    optimizer.watchWindowShortcuts(window)
  })

  // ipcMain.on('ping', () => console.log('pong'));

  ipcMain.handle('select-folder', async (): Promise<string | null> => {
    const result = await dialog.showOpenDialog({
      properties: ['openDirectory']
    })
    if (result.canceled) return null
    const folderPath: string = result.filePaths[0]
    db.run('INSERT OR REPLACE INTO settings (key, value) VALUES (?, ?)', [
      'selected_folder',
      folderPath
    ])

    // Clear photos that are not in the selected folder
    // await new Promise<void>((resolve, reject) => {
    //   db.run('DELETE FROM photos WHERE path NOT LIKE ?', [`${folderPath}%`], (err) => {
    //     if (err) reject(err);
    //     else resolve();
    //   });
    // });

    // First phase: Scan and insert photos without generating thumbnails
    await scanFolderWithoutThumbnails(folderPath)

    // Second phase: Generate thumbnails in background
    setTimeout(() => {
      generateMissingThumbnails(folderPath)
    }, 100) // Small delay to let UI update first

    return folderPath
  })

  ipcMain.handle(
    'get-photos',
    async (
      _event,
      {
        offset,
        limit,
        tags,
        folderPath,
        filterMode
      }: { offset: number; limit: number; tags: string[]; folderPath?: string; filterMode?: 'all' | 'untagged' | 'tagged' }
    ): Promise<Photo[]> => {
      let query: string = 'SELECT id, path, date, thumbnail_path FROM photos'
      const params: (string | number)[] = []

      // Build WHERE conditions
      const conditions: string[] = []

      if (folderPath) {
        conditions.push('path LIKE ?')
        params.push(`${folderPath}%`)
      }

      // Handle filter modes
      if (filterMode === 'untagged') {
        // Show only photos that have no tags
        conditions.push('photos.id NOT IN (SELECT DISTINCT photo_id FROM photo_tags)')
      } else if (filterMode === 'tagged') {
        // Show only photos that have at least one tag
        conditions.push('photos.id IN (SELECT DISTINCT photo_id FROM photo_tags)')
      } else if (filterMode === 'all' && tags && tags.length > 0) {
        // Show photos with specific tags (only in 'all' mode)
        conditions.push(
          'photos.id IN (SELECT photo_id FROM photo_tags JOIN tags ON photo_tags.tag_id = tags.id WHERE tags.name IN (' +
          tags.map(() => '?').join(',') +
          ') GROUP BY photo_id HAVING COUNT(*) = ?)'
        )
        params.push(...tags, tags.length)
      }

      // Add WHERE clause if we have conditions
      if (conditions.length > 0) {
        query += ' WHERE ' + conditions.join(' AND ')
      }

      query += ' ORDER BY date DESC LIMIT ? OFFSET ?'
      params.push(limit, offset)

      return new Promise<Photo[]>((resolve, reject) => {
        db.all(query, params, (err, rows: Photo[]) => {
          if (err) reject(err)
          else resolve(rows)
        })
      })
    }
  )

  // Add missing IPC handlers
  ipcMain.handle(
    'get-folder-tree',
    async (_event, folderPath: string): Promise<FolderNode[] | null> => {
      if (!folderPath) return null
      try {
        return await buildFolderTree(folderPath)
      } catch (error) {
        console.error('Error getting folder tree:', error)
        return null
      }
    }
  )

  ipcMain.handle('get-photo-tags', async (_event, photoId: number): Promise<string[]> => {
    return new Promise((resolve, reject) => {
      db.all(
        `SELECT tags.name FROM tags 
         JOIN photo_tags ON tags.id = photo_tags.tag_id 
         WHERE photo_tags.photo_id = ?`,
        [photoId],
        (err, rows: { name: string }[]) => {
          if (err) {
            console.error('Error getting photo tags:', err)
            reject(err)
          } else {
            resolve(rows.map((row) => row.name))
          }
        }
      )
    })
  })

  ipcMain.handle('get-all-tags', async (): Promise<string[]> => {
    return new Promise((resolve, reject) => {
      db.all('SELECT name FROM tags ORDER BY name', [], (err, rows: { name: string }[]) => {
        if (err) {
          console.error('Error getting all tags:', err)
          reject(err)
        } else {
          resolve(rows.map((row) => row.name))
        }
      })
    })
  })

  ipcMain.handle(
    'add-tag-to-photo',
    async (_event, { photoId, tagName }: { photoId: number; tagName: string }): Promise<void> => {
      return new Promise((resolve, reject) => {
        db.serialize(() => {
          db.run('BEGIN TRANSACTION')

          // First ensure the tag exists
          db.run('INSERT OR IGNORE INTO tags (name) VALUES (?)', [tagName], function (err) {
            if (err) {
              db.run('ROLLBACK')
              console.error('Error adding tag:', err)
              reject(err)
              return
            }

            // Get the tag ID (either existing or newly created)
            db.get('SELECT id FROM tags WHERE name = ?', [tagName], (err, row: { id: number }) => {
              if (err || !row) {
                db.run('ROLLBACK')
                console.error('Error getting tag ID:', err)
                reject(err || new Error('Tag not found'))
                return
              }

              // Add the tag to the photo
              db.run(
                'INSERT OR IGNORE INTO photo_tags (photo_id, tag_id) VALUES (?, ?)',
                [photoId, row.id],
                (err) => {
                  if (err) {
                    db.run('ROLLBACK')
                    console.error('Error linking tag to photo:', err)
                    reject(err)
                  } else {
                    db.run('COMMIT')
                    resolve()
                  }
                }
              )
            })
          })
        })
      })
    }
  )

  ipcMain.handle(
    'remove-tag-from-photo',
    async (_event, { photoId, tagName }: { photoId: number; tagName: string }): Promise<void> => {
      return new Promise((resolve, reject) => {
        db.run(
          `DELETE FROM photo_tags
         WHERE photo_id = ? AND tag_id = (SELECT id FROM tags WHERE name = ?)`,
          [photoId, tagName],
          (err) => {
            if (err) {
              console.error('Error removing tag from photo:', err)
              reject(err)
            } else {
              resolve()
            }
          }
        )
      })
    }
  )

  // Open file in Windows Explorer
  ipcMain.handle('open-file-in-explorer', async (_event, filePath: string): Promise<void> => {
    try {
      await shell.showItemInFolder(filePath)
    } catch (error) {
      console.error('Error opening file in explorer:', error)
      throw error
    }
  })

  // Clear all thumbnail paths from database
  ipcMain.handle('clear-all-thumbnails', async () => {
    try {
      await new Promise<void>((resolve, reject) => {
        db.run('UPDATE photos SET thumbnail_path = NULL', [], (err) => {
          if (err) reject(err)
          else resolve()
        })
      })

      console.log('Cleared all thumbnail paths from database')
      return { success: true, message: 'All thumbnail paths cleared from database' }
    } catch (error) {
      console.error('Error clearing thumbnail paths:', error)
      return { success: false, message: 'Failed to clear thumbnail paths' }
    }
  })

  // Add cleanup function for orphaned thumbnails
  const cleanupThumbnails = async (): Promise<void> => {
    try {
      // Get all thumbnail filenames from the database
      const dbThumbnails: Set<string> = await new Promise((resolve, reject) => {
        db.all(
          'SELECT thumbnail_path FROM photos WHERE thumbnail_path IS NOT NULL',
          [],
          (err, rows: { thumbnail_path: string }[]) => {
            if (err) reject(err)
            else resolve(new Set(rows.map((row) => row.thumbnail_path)))
          }
        )
      })

      // Get all files in the thumbnails directory
      const files = await readdir(thumbnailsDir)

      // Delete files that aren't in the database
      for (const file of files) {
        if (!dbThumbnails.has(file)) {
          try {
            await unlink(pathJoin(thumbnailsDir, file))
            console.log('Deleted orphaned thumbnail:', file)
          } catch (error) {
            console.error('Error deleting thumbnail:', file, error)
          }
        }
      }
    } catch (error) {
      console.error('Error cleaning up thumbnails:', error)
    }
  }

  // Call cleanup on app start
  await cleanupThumbnails()

  createWindow()

  app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) createWindow()
  })

  app.on('will-quit', () => {
    db.close((err) => {
      if (err) console.error('Error closing database:', err.message)
    })
  })
})

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit()
  }
})

const imageExtensions: string[] = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp']

const buildFolderTree = async (rootPath: string): Promise<FolderNode[]> => {
  try {
    const entries = await readdir(rootPath, { withFileTypes: true })
    const nodes: FolderNode[] = []

    for (const entry of entries) {
      if (entry.isDirectory()) {
        const fullPath = pathJoin(rootPath, entry.name)
        try {
          const children = await buildFolderTree(fullPath)
          nodes.push({
            name: entry.name,
            path: fullPath,
            isDirectory: true,
            children
          })
        } catch (error) {
          console.error(`Error processing directory ${fullPath}:`, error)
          // Continue with other directories even if one fails
        }
      }
    }

    // Sort directories alphabetically
    return nodes.sort((a, b) => a.name.localeCompare(b.name))
  } catch (error) {
    console.error(`Error reading directory ${rootPath}:`, error)
    return []
  }
}

// Scan folder and insert photos without generating thumbnails
const scanFolderWithoutThumbnails = async (folderPath: string): Promise<void> => {
  try {
    const existingPaths: Set<string> = await getExistingPaths()
    const files: string[] = await readdir(folderPath)

    for (const file of files) {
      const fullPath: string = pathJoin(folderPath, file)

      try {
        const stat = await statAsync(fullPath)

        if (stat.isDirectory()) {
          await scanFolderWithoutThumbnails(fullPath)
        } else if (stat.isFile()) {
          const ext: string = extname(file).toLowerCase()
          if (imageExtensions.includes(ext)) {
            if (!existingPaths.has(fullPath)) {
              // New photo - insert without thumbnail
              const mtime: string = stat.mtime.toISOString()
              try {
                console.log(`Inserting new photo: ${fullPath}`)
                await insertPhotoWithoutThumbnail(fullPath, mtime)
              } catch (error) {
                console.error(`Error inserting photo ${fullPath}:`, error)
              }
            }
          }
        }
      } catch (error) {
        console.error(`Error processing ${fullPath}:`, error)
      }
    }
  } catch (error) {
    console.error(`Error scanning folder ${folderPath}:`, error)
    throw error
  }
}

// Generate thumbnails for all photos that don't have them
const generateMissingThumbnails = async (folderPath?: string): Promise<void> => {
  try {
    console.log('Starting background thumbnail generation...')

    // Get all photos without thumbnails in the same order as display (date DESC)
    let query = 'SELECT id, path FROM photos WHERE thumbnail_path IS NULL'
    const params: string[] = []

    if (folderPath) {
      query += ' AND path LIKE ?'
      params.push(`${folderPath}%`)
    }

    query += ' ORDER BY date DESC'

    const photosWithoutThumbnails: Photo[] = await new Promise((resolve, reject) => {
      db.all(query, params, (err, rows: Photo[]) => {
        if (err) reject(err)
        else resolve(rows)
      })
    })

    console.log(`Found ${photosWithoutThumbnails.length} photos without thumbnails`)

    // Generate thumbnails one by one
    for (const photo of photosWithoutThumbnails) {
      try {
        if (mainWindow && !mainWindow.isDestroyed()) {
          mainWindow.webContents.send('thumbnail-generation-started', {
            photoId: photo.id,
            photoPath: photo.path
          })
        }

        const thumbnailFilename = await generateThumbnail(photo.path, photo.id)

        if (thumbnailFilename) {
          // Update database with thumbnail path
          await new Promise<void>((resolve, reject) => {
            db.run(
              'UPDATE photos SET thumbnail_path = ? WHERE id = ?',
              [thumbnailFilename, photo.id],
              (err) => {
                if (err) reject(err)
                else resolve()
              }
            )
          })

          console.log(`Updated photo ${photo.id} with thumbnail: ${thumbnailFilename}`)

          // Notify renderer that thumbnail is ready
          if (mainWindow && !mainWindow.isDestroyed()) {
            mainWindow.webContents.send('thumbnail-generated', {
              photoId: photo.id,
              thumbnailPath: thumbnailFilename
            })
          }
        }
      } catch (error) {
        console.error(`Error generating thumbnail for photo ${photo.id}:`, error)
      }
    }

    console.log('Background thumbnail generation completed')
  } catch (error) {
    console.error('Error in generateMissingThumbnails:', error)
  }
}

// const scanFolder = async (folderPath: string): Promise<void> => {
//   try {
//     const existingPaths: Set<string> = await getExistingPaths();
//     const files: string[] = await readdir(folderPath);

//     // Process in batches to avoid overwhelming the system
//     const BATCH_SIZE = 10;
//     const batches = [];

//     for (let i = 0; i < files.length; i += BATCH_SIZE) {
//       batches.push(files.slice(i, i + BATCH_SIZE));
//     }

//     for (const batch of batches) {
//       await Promise.all(
//         batch.map(async (file) => {
//           try {
//             const fullPath: string = pathJoin(folderPath, file);
//             const stat = await statAsync(fullPath);

//             if (stat.isDirectory()) {
//               await scanFolder(fullPath);
//             } else if (stat.isFile()) {
//               const ext: string = extname(file).toLowerCase();
//               if (imageExtensions.includes(ext)) {
//                 if (!existingPaths.has(fullPath)) {
//                   // New photo - insert and generate thumbnail
//                   const mtime: string = stat.mtime.toISOString();
//                   try {
//                     console.log(`Processing new photo: ${fullPath}`);
//                     const id: number = await insertPhotoWithoutThumbnail(fullPath, mtime);
//                     console.log(`Inserted photo with ID: ${id}`);

//                     // Notify renderer that thumbnail generation started
//                     if (mainWindow && !mainWindow.isDestroyed()) {
//                       mainWindow.webContents.send('thumbnail-generation-started', {
//                         photoId: id,
//                         photoPath: fullPath
//                       });
//                     }

//                     const thumbnailFilename: string | undefined = await generateThumbnail(fullPath, id);
//                     console.log(`Generated thumbnail: ${thumbnailFilename}`);
//                     if (thumbnailFilename) {
//                       await updatePhotoThumbnail(id, thumbnailFilename);
//                       console.log(`Updated photo ${id} with thumbnail: ${thumbnailFilename}`);

//                       // Notify renderer that thumbnail is ready
//                       if (mainWindow && !mainWindow.isDestroyed()) {
//                         mainWindow.webContents.send('thumbnail-generated', {
//                           photoId: id,
//                           thumbnailPath: thumbnailFilename
//                         });
//                       }
//                     }
//                   } catch (error) {
//                     console.error(`Error processing file ${fullPath}:`, error);
//                     // Continue with other files even if one fails
//                   }
//                 } else {
//                   // Existing photo - check if it needs a thumbnail
//                   try {
//                     const photoData = await getPhotoByPath(fullPath);
//                     if (photoData && !photoData.thumbnail_path) {
//                       console.log(`Generating missing thumbnail for existing photo: ${fullPath}`);

//                       // Notify renderer that thumbnail generation started
//                       if (mainWindow && !mainWindow.isDestroyed()) {
//                         mainWindow.webContents.send('thumbnail-generation-started', {
//                           photoId: photoData.id,
//                           photoPath: fullPath
//                         });
//                       }

//                       const thumbnailFilename: string | undefined = await generateThumbnail(fullPath, photoData.id);
//                       console.log(`Generated thumbnail: ${thumbnailFilename}`);
//                       if (thumbnailFilename) {
//                         await updatePhotoThumbnail(photoData.id, thumbnailFilename);
//                         console.log(`Updated existing photo ${photoData.id} with thumbnail: ${thumbnailFilename}`);

//                         // Notify renderer that thumbnail is ready
//                         if (mainWindow && !mainWindow.isDestroyed()) {
//                           mainWindow.webContents.send('thumbnail-generated', {
//                             photoId: photoData.id,
//                             thumbnailPath: thumbnailFilename
//                           });
//                         }
//                       }
//                     }
//                   } catch (error) {
//                     console.error(`Error generating thumbnail for existing photo ${fullPath}:`, error);
//                   }
//                 }
//               }
//             }
//           } catch (error) {
//             console.error(`Error processing file ${file} in ${folderPath}:`, error);
//             // Continue with other files even if one fails
//           }
//         })
//       );
//     }
//   } catch (error) {
//     console.error(`Error scanning folder ${folderPath}:`, error);
//     throw error; // Propagate error to caller
//   }
// };

const getExistingPaths = (): Promise<Set<string>> => {
  return new Promise((resolve, reject) => {
    db.all('SELECT path FROM photos', [], (err, rows: { path: string }[]) => {
      if (err) reject(err)
      else resolve(new Set(rows.map((row) => row.path)))
    })
  })
}

// const getPhotoByPath = (photoPath: string): Promise<Photo | null> => {
//   return new Promise((resolve, reject) => {
//     db.get('SELECT id, path, date, thumbnail_path FROM photos WHERE path = ?', [photoPath], (err, row: Photo) => {
//       if (err) reject(err);
//       else resolve(row || null);
//     });
//   });
// };

const insertPhotoWithoutThumbnail = (photoPath: string, date: string): Promise<number> => {
  return new Promise((resolve, reject) => {
    db.run('INSERT INTO photos (path, date) VALUES (?, ?)', [photoPath, date], function (err) {
      if (err) reject(err)
      else resolve(this.lastID)
    })
  })
}

const generateThumbnail = async (photoPath: string, id: number): Promise<string | undefined> => {
  try {
    console.log(`Starting thumbnail generation for: ${photoPath}`)
    // Validate file path to prevent path traversal
    const normalizedPath = normalize(photoPath)
    if (normalizedPath.includes('..')) {
      throw new Error('Invalid file path')
    }

    const filename: string = `${id}_thumb.jpg`
    const thumbnailPath: string = pathJoin(thumbnailsDir, filename)
    console.log(`Thumbnail will be saved to: ${thumbnailPath}`)

    await sharp(photoPath)
      .resize(200, 200, { fit: 'inside', withoutEnlargement: true })
      .toFormat('jpg')
      .toFile(thumbnailPath)

    console.log(`Thumbnail generated successfully: ${filename}`)
    return filename
  } catch (err) {
    console.error('Error generating thumbnail for', photoPath, err)

    // Create a placeholder thumbnail for failed images
    try {
      const filename: string = `${id}_thumb_placeholder.jpg`
      const placeholderPath: string = pathJoin(thumbnailsDir, filename)

      // Create a simple gray placeholder image
      await sharp({
        create: {
          width: 200,
          height: 200,
          channels: 3,
          background: { r: 200, g: 200, b: 200 }
        }
      })
        .jpeg()
        .toFile(placeholderPath)

      return filename
    } catch (placeholderErr) {
      console.error('Error creating placeholder thumbnail:', placeholderErr)
      return undefined
    }
  }
}

// const updatePhotoThumbnail = (id: number, thumbnailFilename?: string): Promise<void> => {
//   return new Promise((resolve, reject) => {
//     db.run('UPDATE photos SET thumbnail_path = ? WHERE id = ?', [thumbnailFilename, id], (err) => {
//       if (err) reject(err);
//       else resolve();
//     });
//   });
// };
